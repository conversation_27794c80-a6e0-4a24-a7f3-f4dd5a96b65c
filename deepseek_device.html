<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>闲置服务器云平台 - 接入与管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #4361ee;
            --primary-light: #5c7cfa;
            --secondary: #3f37c9;
            --success: #4cc9f0;
            --success-light: #56d3fb;
            --dark: #1d3557;
            --light: #f8f9fa;
            --gray: #6c757d;
            --light-gray: #e9ecef;
            --warning: #ffb703;
            --danger: #e63946;
            --card-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            --hover-shadow: 0 8px 25px rgba(67, 97, 238, 0.2);
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #f0f5ff 0%, #e6f0ff 100%);
            color: #333;
            line-height: 1.6;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 0;
            margin-bottom: 30px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            animation: fadeInDown 0.8s ease;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo h1 {
            font-size: 28px;
            color: var(--dark);
            font-weight: 700;
        }

        .logo i {
            font-size: 32px;
            color: var(--primary);
            background: rgba(67, 97, 238, 0.1);
            padding: 12px;
            border-radius: 12px;
            transition: var(--transition);
        }

        .logo:hover i {
            transform: rotate(15deg);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
            position: relative;
            cursor: pointer;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--primary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 20px;
            transition: var(--transition);
        }

        .user-info:hover .user-avatar {
            transform: scale(1.05);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.3);
        }

        .dashboard {
            display: grid;
            grid-template-columns: 280px 1fr;
            gap: 25px;
        }

        .sidebar {
            background: white;
            border-radius: 16px;
            padding: 25px;
            box-shadow: var(--card-shadow);
            height: fit-content;
            animation: fadeInLeft 0.8s ease;
        }

        .sidebar h3 {
            margin-bottom: 20px;
            color: var(--dark);
            font-size: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-item {
            padding: 12px 15px;
            border-radius: 10px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 500;
            position: relative;
        }

        .nav-item:hover, .nav-item.active {
            background: rgba(67, 97, 238, 0.1);
            color: var(--primary);
        }

        .nav-item::after {
            content: '';
            position: absolute;
            right: 15px;
            width: 8px;
            height: 8px;
            background: var(--primary);
            border-radius: 50%;
            opacity: 0;
            transition: var(--transition);
        }

        .nav-item.active::after {
            opacity: 1;
        }

        .nav-item i {
            width: 24px;
            text-align: center;
        }

        .main-content {
            display: flex;
            flex-direction: column;
            gap: 25px;
            animation: fadeInUp 0.8s ease;
        }

        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 20px;
        }

        .stat-card {
            background: white;
            border-radius: 16px;
            padding: 25px;
            box-shadow: var(--card-shadow);
            display: flex;
            flex-direction: column;
            gap: 15px;
            transition: var(--transition);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--hover-shadow);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: var(--primary);
            transform: scaleX(0);
            transform-origin: left;
            transition: transform 0.5s ease;
        }

        .stat-card:hover::before {
            transform: scaleX(1);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .bg-primary {
            background: rgba(67, 97, 238, 0.15);
            color: var(--primary);
        }

        .bg-success {
            background: rgba(76, 201, 240, 0.15);
            color: var(--success);
        }

        .bg-warning {
            background: rgba(255, 183, 3, 0.15);
            color: var(--warning);
        }

        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: var(--dark);
        }

        .stat-title {
            color: var(--gray);
            font-size: 16px;
        }

        .card {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: var(--card-shadow);
            transition: var(--transition);
        }

        .card:hover {
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.12);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .card-title {
            font-size: 22px;
            color: var(--dark);
            font-weight: 700;
        }

        .btn {
            padding: 12px 24px;
            border-radius: 10px;
            border: none;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background: var(--secondary);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
        }

        .btn-outline {
            background: transparent;
            border: 2px solid var(--primary);
            color: var(--primary);
        }

        .btn-outline:hover {
            background: rgba(67, 97, 238, 0.1);
        }

        .tabs {
            display: flex;
            border-bottom: 2px solid rgba(0, 0, 0, 0.05);
            margin-bottom: 25px;
        }

        .tab {
            padding: 12px 25px;
            cursor: pointer;
            font-weight: 600;
            color: var(--gray);
            position: relative;
            transition: var(--transition);
        }

        .tab:hover {
            color: var(--primary);
        }

        .tab.active {
            color: var(--primary);
        }

        .tab.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 3px;
            background: var(--primary);
            animation: tabUnderline 0.3s ease;
        }

        .tab-content {
            display: none;
            animation: fadeIn 0.5s ease;
        }

        .tab-content.active {
            display: block;
        }

        .form-group {
            margin-bottom: 20px;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--dark);
        }

        .form-control {
            width: 100%;
            padding: 14px;
            border: 1px solid #ddd;
            border-radius: 10px;
            font-size: 16px;
            transition: var(--transition);
        }

        .form-control:focus {
            border-color: var(--primary);
            outline: none;
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.2);
        }

        .form-control:focus + .input-icon {
            color: var(--primary);
        }

        .grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .devices-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        .devices-category {
            margin-bottom: 30px;
        }
        .category-title {
            font-size: 20px;
            font-weight: 700;
            color: var(--dark);
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid var(--primary);
            display: flex;
            align-items: center;
            gap: 10px;
        }


        .device-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: var(--card-shadow);
            border: 2px solid #eee;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .device-card:hover {
            border-color: var(--primary);
            transform: translateY(-5px);
            box-shadow: var(--hover-shadow);
        }

        .device-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .device-name {
            font-weight: 700;
            font-size: 18px;
            color: var(--dark);
        }

        .device-status {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-online {
            background: rgba(76, 201, 240, 0.15);
            color: var(--success);
        }

        .status-offline {
            background: rgba(230, 57, 70, 0.15);
            color: var(--danger);
        }

        .device-specs {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
            flex-grow: 1;
        }

        .spec-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .spec-label {
            font-size: 13px;
            color: var(--gray);
        }

        .spec-value {
            font-weight: 600;
            font-size: 16px;
        }

        .device-config {
            border-top: 1px solid var(--light-gray);
            padding-top: 15px;
            margin-top: auto;
        }

        .exclusive-toggle {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .exclusive-toggle label {
            font-weight: 600;
            color: var(--dark);
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 28px;
        }
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 28px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 20px;
            width: 20px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: var(--primary);
        }
        input:checked + .slider:before {
            transform: translateX(22px);
        }

        .device-actions {
            display: flex;
            gap: 10px;
        }

        .btn-sm {
            padding: 10px 15px;
            font-size: 14px;
            flex: 1;
            text-align: center;
            transition: var(--transition);
            border-radius: 8px;
            border: none;
            cursor: pointer;
        }

        .btn-sm:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
        }

        .btn-success {
            background: var(--success);
            color: white;
        }

        .btn-success:hover {
            background: var(--success-light);
        }

        .btn-warning {
            background: var(--warning);
            color: white;
        }

        .guide-container {
            background: rgba(67, 97, 238, 0.05);
            border-radius: 12px;
            padding: 25px;
            margin-top: 20px;
        }

        .guide-steps {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-top: 20px;
        }

        .guide-step {
            display: flex;
            gap: 15px;
            opacity: 0.8;
            transition: var(--transition);
        }

        .guide-step.active {
            opacity: 1;
        }

        .guide-step:hover {
            transform: translateX(5px);
        }

        .step-number {
            width: 32px;
            height: 32px;
            background: var(--primary);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            flex-shrink: 0;
            transition: var(--transition);
        }

        .guide-step:hover .step-number {
            transform: scale(1.1);
        }

        .step-content {
            flex: 1;
        }

        .step-title {
            font-weight: 600;
            margin-bottom: 5px;
            color: var(--dark);
        }

        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 14px;
            line-height: 1.5;
            overflow-x: auto;
            position: relative;
        }

        .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255,255,255,0.1);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            cursor: pointer;
            font-size: 12px;
            transition: var(--transition);
        }

        .copy-btn:hover {
            background: rgba(255,255,255,0.2);
        }

        .token-display {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 15px;
            padding: 12px 15px;
            background: rgba(67, 97, 238, 0.05);
            border-radius: 8px;
            font-weight: 600;
            color: var(--primary);
        }

        .token-display i {
            font-size: 18px;
        }

        .os-tags {
            display: flex;
            gap: 10px;
            margin: 15px 0;
        }

        .os-tag {
            padding: 8px 15px;
            border-radius: 20px;
            background: rgba(67, 97, 238, 0.1);
            color: var(--primary);
            font-size: 14px;
            font-weight: 500;
            transition: var(--transition);
        }

        .os-tag:hover {
            background: rgba(67, 97, 238, 0.2);
            transform: translateY(-2px);
        }

        .compatibility-check {
            background: rgba(255, 183, 3, 0.05);
            border-left: 4px solid var(--warning);
            padding: 15px;
            border-radius: 0 8px 8px 0;
            margin: 15px 0;
        }

        .compatibility-title {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 10px;
            color: var(--warning);
            font-weight: 600;
        }

        .check-list {
            list-style: none;
            margin-left: 5px;
        }

        .check-list li {
            padding: 5px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .check-list li i {
            color: var(--success);
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
            animation: fadeIn 0.3s ease;
        }

        .modal-content {
            background: white;
            border-radius: 16px;
            width: 90%;
            max-width: 550px;
            padding: 30px;
            box-shadow: 0 10px 50px rgba(0, 0, 0, 0.2);
            position: relative;
            animation: modalIn 0.4s ease;
        }

        .close-modal {
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 24px;
            cursor: pointer;
            color: var(--gray);
            transition: var(--transition);
        }

        .close-modal:hover {
            color: var(--danger);
            transform: rotate(90deg);
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 10px;
            background: white;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
            display: flex;
            align-items: center;
            gap: 15px;
            z-index: 2000;
            transform: translateX(120%);
            transition: transform 0.4s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification-success {
            border-left: 4px solid var(--success);
        }

        .notification-error {
            border-left: 4px solid var(--danger);
        }

        .notification i {
            font-size: 24px;
        }

        .notification-success i {
            color: var(--success);
        }

        .notification-error i {
            color: var(--danger);
        }


        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInLeft {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes tabUnderline {
            from { width: 0; }
            to { width: 100%; }
        }

        @keyframes modalIn {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }

            .grid-2 {
                grid-template-columns: 1fr;
            }

            .stats-cards {
                grid-template-columns: 1fr;
            }

            header {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .user-info {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <i class="fas fa-server"></i>
                <h1>闲置服务器云平台</h1>
            </div>
            <div class="user-info">
                <div class="user-avatar">JD</div>
                <div>
                    <div class="user-name">John Doe</div>
                    <div class="user-role">企业管理员</div>
                </div>
            </div>
        </header>

        <div class="dashboard">
            <div class="sidebar">
                <h3><i class="fas fa-cog"></i> 管理面板</h3>
                <ul class="nav-menu">
                    <li class="nav-item active"><i class="fas fa-tachometer-alt"></i> 控制面板</li>
                    <li class="nav-item"><i class="fas fa-plug"></i> 设备接入</li>
                    <li class="nav-item"><i class="fas fa-server"></i> 我的设备</li>
                    <li class="nav-item"><i class="fas fa-store"></i> 算力市场</li>
                    <li class="nav-item"><i class="fas fa-chart-line"></i> 资源监控</li>
                    <li class="nav-item"><i class="fas fa-wallet"></i> 收益管理</li>
                    <li class="nav-item"><i class="fas fa-cog"></i> 系统设置</li>
                    <li class="nav-item"><i class="fas fa-question-circle"></i> 帮助中心</li>
                </ul>
            </div>

            <div class="main-content">
                <div class="stats-cards">
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon bg-primary">
                                <i class="fas fa-server"></i>
                            </div>
                        </div>
                        <div class="stat-value">8</div>
                        <div class="stat-title">已接入设备</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon bg-success">
                                <i class="fas fa-microchip"></i>
                            </div>
                        </div>
                        <div class="stat-value">124 核</div>
                        <div class="stat-title">总计算核心</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon bg-warning">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                        </div>
                        <div class="stat-value">¥1,248</div>
                        <div class="stat-title">本月收益</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon bg-primary">
                                <i class="fas fa-chart-line"></i>
                            </div>
                        </div>
                        <div class="stat-value">86%</div>
                        <div class="stat-title">资源利用率</div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">设备接入</h2>
                    </div>

                    <div class="tabs">
                        <div class="tab active" data-tab="ssh">SSH授权接入</div>
                        <div class="tab" data-tab="manual">设备端手动接入</div>
                    </div>

                    <div class="tab-content active" id="ssh-tab">
                        <div class="grid-2">
                            <div class="form-group">
                                <label for="server-name">服务器名称</label>
                                <input type="text" id="server-name" class="form-control" placeholder="例如：研发服务器-01">
                            </div>
                            <div class="form-group">
                                <label for="server-ip">IP地址</label>
                                <input type="text" id="server-ip" class="form-control" placeholder="例如：*************">
                            </div>
                        </div>

                        <div class="grid-2">
                            <div class="form-group">
                                <label for="ssh-username">SSH用户名</label>
                                <input type="text" id="ssh-username" class="form-control" placeholder="例如：root">
                            </div>
                            <div class="form-group">
                                <label for="ssh-password">SSH密码/密钥</label>
                                <input type="password" id="ssh-password" class="form-control" placeholder="输入密码或上传密钥">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="ssh-port">SSH端口</label>
                            <input type="number" id="ssh-port" class="form-control" value="22">
                        </div>

                        <button class="btn btn-primary" id="connect-ssh">
                            <i class="fas fa-plug"></i> 授权并接入设备
                        </button>
                    </div>

                    <div class="tab-content" id="manual-tab">
                        <p>在您的Linux设备上执行以下步骤完成手动接入：</p>
                        <div class="guide-container">
                            <div class="guide-steps">
                               <div class="guide-step active" data-step="1">
                                    <div class="step-number">1</div>
                                    <div class="step-content">
                                        <div class="step-title">下载并运行安装脚本</div>
                                        <p>在您的设备终端中，运行以下一键安装命令即可完成代理安装与激活。</p>
                                        <div class="code-block">
                                            $ curl -sSL https://cloud-platform.com/install.sh | sudo bash -s -- --token 8d7a6b5c4d3e2f1a
                                            <button class="copy-btn">复制</button>
                                        </div>
                                        <div class="token-display">
                                            <i class="fas fa-key"></i>
                                            <div>您的专属激活码：<strong>8d7a6b5c4d3e2f1a</strong></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="guide-step" data-step="2">
                                    <div class="step-number">2</div>
                                    <div class="step-content">
                                        <div class="step-title">兼容性与要求</div>
                                        <p>安装脚本会自动进行硬件与环境预检。请确保您的设备满足以下基本要求：</p>
                                        <div class="os-tags">
                                            <div class="os-tag">Ubuntu 18.04+</div>
                                            <div class="os-tag">CentOS 7+</div>
                                            <div class="os-tag">Debian 10+</div>
                                        </div>
                                        <div class="compatibility-check">
                                            <div class="compatibility-title">
                                                <i class="fas fa-clipboard-check"></i> 关键检查项
                                            </div>
                                            <ul class="check-list">
                                                <li><i class="fas fa-check-circle"></i> 64位Linux操作系统</li>
                                                <li><i class="fas fa-check-circle"></i> 内存 ≥ 8GB</li>
                                                <li><i class="fas fa-check-circle"></i> Docker引擎已安装</li>
                                                <li><i class="fas fa-check-circle"></i> [GPU设备] NVIDIA驱动 ≥ 450.80.02</li>
                                                <li><i class="fas fa-check-circle"></i> [GPU设备] CUDA版本 ≥ 11.0</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                               <div class="guide-step" data-step="3">
                                    <div class="step-number">3</div>
                                    <div class="step-content">
                                        <div class="step-title">验证设备接入</div>
                                        <p>安装完成后，代理将自动上报设备信息并执行基准测试。该过程通常需要1-3分钟，完成后您的设备将出现在下方的"我的设备"列表中。</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">我的设备</h2>
                        <div>
                            <button class="btn btn-outline" id="refresh-devices">
                                <i class="fas fa-sync-alt"></i> 刷新状态
                            </button>
                            <button class="btn btn-primary" id="add-device">
                                <i class="fas fa-plus"></i> 添加设备
                            </button>
                        </div>
                    </div>

                    <div id="devices-container">
                        </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal" id="device-modal">
        <div class="modal-content">
            <span class="close-modal">&times;</span>
            <h2 style="margin-bottom:20px">设备详情</h2>
            <div id="device-details"></div>
        </div>
    </div>

    <div class="modal" id="listing-modal">
        <div class="modal-content">
            <span class="close-modal">&times;</span>
            <h2 id="listing-modal-title" style="margin-bottom:20px">配置上架策略</h2>
            <input type="hidden" id="listing-device-id">

            <div class="grid-2">
                <div class="form-group">
                    <label for="price-model">售卖形式</label>
                    <select id="price-model" class="form-control">
                        <option value="core-hour">核心数/小时</option>
                        <option value="gpu-hour">单显卡/小时</option>
                        <option value="full-server">整机/小时</option>
                        <option value="resource-combo">自定义资源组合</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="price">价格 (¥/单位)</label>
                    <input type="number" id="price" class="form-control" placeholder="例如：0.85">
                </div>
            </div>

            <div class="form-group">
                <label for="min-lease">最小租用时间</label>
                <select id="min-lease" class="form-control">
                    <option value="1">1小时</option>
                    <option value="4">4小时</option>
                    <option value="8">8小时</option>
                    <option value="24">24小时</option>
                    <option value="72">72小时</option>
                </select>
            </div>

            <div class="form-group">
                <label for="availability">可用时间段</label>
                <div style="display:flex; gap:10px; align-items:center;">
                    <input type="time" class="form-control" value="00:00">
                    <span>至</span>
                    <input type="time" class="form-control" value="23:59">
                </div>
            </div>

            <button class="btn btn-primary" id="confirm-listing" style="width:100%">
                <i class="fas fa-check-circle"></i> 确认并上架
            </button>
        </div>
    </div>

    <div class="notification" id="notification">
        <i class="fas fa-check-circle"></i>
        <div class="notification-content">操作成功！</div>
    </div>

    <script>
        // 设备数据
        const devices = [
            {
                id: 1,
                name: "AI训练服务器-01",
                status: "online",
                exclusive: false, // Replaces usage
                cpu: "AMD EPYC 32核",
                memory: "128 GB",
                gpu: "2×RTX 4090",
                storage: "2 TB NVMe",
                type: "GPU",
                ip: "*************",
                location: "上海数据中心"
            },
            {
                id: 2,
                name: "渲染节点-04",
                status: "online",
                exclusive: true, // Replaces usage
                cpu: "Intel Xeon 16核",
                memory: "64 GB",
                gpu: "RTX A6000",
                storage: "1 TB SSD",
                type: "GPU",
                ip: "*************",
                location: "北京数据中心"
            },
            {
                id: 3,
                name: "数据分析服务器",
                status: "offline",
                exclusive: true, // Replaces usage
                cpu: "AMD Threadripper 64核",
                memory: "256 GB",
                gpu: "无",
                storage: "8 TB HDD",
                type: "CPU",
                ip: "*************",
                location: "广州数据中心"
            },
            {
                id: 4,
                name: "GPU计算节点-07",
                status: "online",
                exclusive: false, // Replaces usage
                cpu: "Intel i9 16核",
                memory: "64 GB",
                gpu: "4×RTX 3090",
                storage: "4 TB NVMe",
                type: "GPU",
                ip: "*************",
                location: "上海数据中心"
            },
            {
                id: 5,
                name: "Web编译服务器",
                status: "online",
                exclusive: true,
                cpu: "Intel Xeon 32核",
                memory: "64 GB",
                gpu: "无",
                storage: "512 GB NVMe",
                type: "CPU",
                ip: "************",
                location: "北京数据中心"
            }
        ];

        // DOM 元素
        const devicesContainer = document.getElementById('devices-container');
        const deviceModal = document.getElementById('device-modal');
        const listingModal = document.getElementById('listing-modal');
        const deviceDetails = document.getElementById('device-details');
        const notification = document.getElementById('notification');

        // 初始化页面
        document.addEventListener('DOMContentLoaded', () => {
            renderDevices();
            setupEventListeners();
        });

        // 渲染设备卡片
        function renderDevices() {
            devicesContainer.innerHTML = ''; // Clear previous content

            const gpuDevices = devices.filter(d => d.type === 'GPU');
            const cpuDevices = devices.filter(d => d.type === 'CPU');

            const createCategorySection = (title, icon, deviceList) => {
                if (deviceList.length === 0) return '';

                const deviceCards = deviceList.map(device => createDeviceCard(device)).join('');

                return `
                    <div class="devices-category">
                        <h3 class="category-title"><i class="fas ${icon}"></i> ${title}</h3>
                        <div class="devices-list">
                            ${deviceCards}
                        </div>
                    </div>
                `;
            };

            devicesContainer.innerHTML =
                createCategorySection('GPU 服务器', 'fa-microchip', gpuDevices) +
                createCategorySection('CPU 服务器', 'fa-server', cpuDevices);
        }

        function createDeviceCard(device) {
             return `
                <div class="device-card" data-id="${device.id}">
                    <div class="device-header">
                        <div class="device-name">${device.name}</div>
                        <div class="device-status ${device.status === 'online' ? 'status-online' : 'status-offline'}">
                            <i class="fas fa-circle"></i> ${device.status === 'online' ? '在线' : '离线'}
                        </div>
                    </div>
                    <div class="device-specs">
                        <div class="spec-item"><div class="spec-label">CPU</div><div class="spec-value">${device.cpu}</div></div>
                        <div class="spec-item"><div class="spec-label">内存</div><div class="spec-value">${device.memory}</div></div>
                        <div class="spec-item"><div class="spec-label">GPU</div><div class="spec-value">${device.gpu}</div></div>
                        <div class="spec-item"><div class="spec-label">存储</div><div class="spec-value">${device.storage}</div></div>
                    </div>
                    <div class="device-config">
                        <div class="exclusive-toggle">
                            <label for="exclusive-${device.id}">独享使用</label>
                            <label class="switch">
                                <input type="checkbox" id="exclusive-${device.id}" class="exclusive-checkbox" ${device.exclusive ? 'checked' : ''} ${device.status === 'offline' ? 'disabled' : ''}>
                                <span class="slider"></span>
                            </label>
                        </div>
                        <div class="device-actions">
                            ${device.status === 'offline' ? `
                                <button class="btn btn-sm btn-warning power-btn">
                                    <i class="fas fa-power-off"></i> 启动
                                </button>
                            ` : `
                                <button class="btn btn-sm btn-outline view-details-btn">
                                    <i class="fas fa-eye"></i> 查看详情
                                </button>
                                <button class="btn btn-sm btn-success list-btn" ${device.exclusive ? 'disabled' : ''}>
                                    <i class="fas fa-store"></i> ${device.exclusive === false ? '上架策略' : '上架到市场'}
                                </button>
                            `}
                        </div>
                    </div>
                </div>
            `;
        }

        // 设置事件监听器
        function setupEventListeners() {
            // 标签切换
            document.querySelectorAll('.tab').forEach(tab => {
                tab.addEventListener('click', () => {
                    document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                    document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                    tab.classList.add('active');
                    document.getElementById(`${tab.getAttribute('data-tab')}-tab`).classList.add('active');
                });
            });

            // 设备卡片事件委托
            devicesContainer.addEventListener('click', (e) => {
                const card = e.target.closest('.device-card');
                if (!card) return;

                const deviceId = parseInt(card.dataset.id);
                const device = devices.find(d => d.id === deviceId);

                if (e.target.closest('.exclusive-checkbox')) {
                    toggleExclusive(device, e.target.closest('.exclusive-checkbox').checked);
                } else if (e.target.closest('.list-btn')) {
                    openListingModal(device);
                } else if (e.target.closest('.power-btn')) {
                    powerOnDevice(device);
                } else if (e.target.closest('.view-details-btn')) {
                    showDeviceDetails(device);
                }
            });

            // 刷新设备按钮
            document.getElementById('refresh-devices').addEventListener('click', () => {
                showNotification('设备状态已刷新', 'success');
                renderDevices();
            });

            // 添加设备按钮
            document.getElementById('add-device').addEventListener('click', () => {
                document.querySelector('.tab[data-tab="manual"]').click();
                window.scrollTo({
                    top: document.querySelector('.card').offsetTop - 20,
                    behavior: 'smooth'
                });
            });

            // SSH连接按钮
            document.getElementById('connect-ssh').addEventListener('click', () => {
                showNotification('正在通过SSH连接设备...', 'success');
                setTimeout(() => showNotification('设备连接成功', 'success'), 1500);
            });

            // 确认上架按钮
            document.getElementById('confirm-listing').addEventListener('click', () => {
                const deviceId = parseInt(document.getElementById('listing-device-id').value);
                const device = devices.find(d => d.id === deviceId);
                showNotification(`${device.name} 已成功上架到市场`, 'success');
                listingModal.style.display = 'none';
            });

            // 关闭模态框
            document.querySelectorAll('.close-modal').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.target.closest('.modal').style.display = 'none';
                });
            });

            // 点击模态框外部关闭
            window.addEventListener('click', (e) => {
                if (e.target.classList.contains('modal')) {
                    e.target.style.display = 'none';
                }
            });

            // 复制按钮
            document.querySelectorAll('.copy-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const codeBlock = this.parentElement;
                    const textToCopy = codeBlock.innerText.replace('复制', '').trim();
                    navigator.clipboard.writeText(textToCopy).then(() => {
                        const originalText = this.textContent;
                        this.textContent = '已复制!';
                        this.style.background = 'rgba(76, 201, 240, 0.5)';
                        setTimeout(() => {
                            this.textContent = originalText;
                            this.style.background = 'rgba(255,255,255,0.1)';
                        }, 2000);
                    });
                });
            });
        }

        // 切换独享模式
        function toggleExclusive(device, isExclusive) {
            device.exclusive = isExclusive;
            if (isExclusive) {
                showNotification(`${device.name} 已设为独享使用`, 'success');
            } else {
                showNotification(`${device.name} 已取消独享，可上架到市场`, 'success');
            }
            renderDevices(); // Re-render to update button states
        }

        // 打开上架配置模态框
        function openListingModal(device) {
            document.getElementById('listing-modal-title').innerText = `配置 "${device.name}" 的上架策略`;
            document.getElementById('listing-device-id').value = device.id;
            listingModal.style.display = 'flex';
        }

        // 显示设备详情
        function showDeviceDetails(device) {
            deviceDetails.innerHTML = `
                <div style="display:flex; gap:20px; margin-bottom:20px;">
                    <div style="width:80px; height:80px; background:var(--primary);
                        border-radius:12px; display:flex; align-items:center; justify-content:center; color:white; font-size:32px;">
                        <i class="fas ${device.type === 'GPU' ? 'fa-microchip' : 'fa-server'}"></i>
                    </div>
                    <div>
                        <h3>${device.name}</h3>
                        <div style="display:flex; gap:10px; margin-top:5px; align-items:center;">
                            <span class="device-status ${device.status === 'online' ? 'status-online' : 'status-offline'}" style="margin:0">
                                ${device.status === 'online' ? '在线' : '离线'}
                            </span>
                            <span style="background:var(--light-gray); padding: 3px 8px; border-radius: 6px; font-size: 13px;">${device.type} 服务器</span>
                        </div>
                    </div>
                </div>
                <div class="grid-2" style="margin-bottom:20px;">
                    <div class="form-group"><label>IP地址</label><div class="form-control" style="background:var(--light-gray);">${device.ip}</div></div>
                    <div class="form-group"><label>位置</label><div class="form-control" style="background:var(--light-gray);">${device.location}</div></div>
                </div>
                <div class="form-group">
                    <label>硬件规格</label>
                    <div style="background:var(--light-gray); border-radius:10px; padding:15px;">
                        <div style="display:flex; justify-content:space-between; padding:8px 0; border-bottom:1px solid #ddd;"><span>CPU</span><span>${device.cpu}</span></div>
                        <div style="display:flex; justify-content:space-between; padding:8px 0; border-bottom:1px solid #ddd;"><span>内存</span><span>${device.memory}</span></div>
                        <div style="display:flex; justify-content:space-between; padding:8px 0; border-bottom:1px solid #ddd;"><span>GPU</span><span>${device.gpu}</span></div>
                        <div style="display:flex; justify-content:space-between; padding:8px 0;"><span>存储</span><span>${device.storage}</span></div>
                    </div>
                </div>
            `;
            deviceModal.style.display = 'flex';
        }

        // 启动设备
        function powerOnDevice(device) {
            device.status = 'online';
            device.exclusive = true; // Default to exclusive when powered on
            renderDevices();
            showNotification(`${device.name} 已启动`, 'success');
        }

        // 显示通知
        function showNotification(message, type) {
            notification.className = `notification ${type === 'success' ? 'notification-success' : 'notification-error'}`;
            notification.querySelector('.notification-content').textContent = message;
            notification.querySelector('i').className = `fas ${type === 'success' ? 'fa-check-circle' : 'fa-times-circle'}`;
            notification.classList.add('show');
            setTimeout(() => notification.classList.remove('show'), 3000);
        }
    </script>
</body>
</html>
