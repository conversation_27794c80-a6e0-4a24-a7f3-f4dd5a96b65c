import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react-swc';
import path from 'path';
import { componentTagger } from 'lovable-tagger';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: '::',
    port: 8080,
    proxy: {
      '/basic-api': {
        target: 'http://ihmp-service-dev.infra.sail-cloud.com/',
        // target: 'http://************:10002/',
        changeOrigin: true,
        secure: false,
        rewrite: path => path.replace(/^\/basic-api/, ''),
      },
    },
  },
  plugins: [react(), mode === 'development' && componentTagger()].filter(
    <PERSON><PERSON>an
  ),
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
}));
