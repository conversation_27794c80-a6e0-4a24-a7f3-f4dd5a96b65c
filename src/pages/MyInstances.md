# MyInstances 页面说明

## 概述

`MyInstances.jsx` 是一个完整的实例管理页面，已经从 TypeScript 转换为 JSX 格式，并对接了真实的后端接口。

## 主要功能

### 1. 统计数据展示
- 总实例数
- 运行中实例数
- 已停止实例数
- 总费用统计

### 2. 搜索和筛选功能
- **搜索**：按实例名称搜索
- **状态筛选**：运行中/已停止/启动中
- **应用类型筛选**：深度学习/数据分析/Web应用
- **创建时间筛选**：日期范围选择

### 3. 实例列表
- 实例卡片展示
- 硬件配置信息（CPU、内存、GPU、存储）
- 应用信息展示
- 实例操作按钮（启动/停止/重启/删除/SSH/详情）

### 4. 分页功能
- 支持分页显示
- 可调整每页显示数量
- 显示总数统计

### 5. 实例详情对话框
- 基本信息
- 硬件配置
- SSH连接信息
- 监控数据
- 应用管理
- 实例设置

## 接口对接

### 1. 实例列表接口
- **接口地址**: `/ihmp/app/deploy/page`
- **请求方式**: `GET`
- **功能**: 获取分页的实例列表数据

### 2. 统计数据接口
- **接口地址**: `/ihmp/app/deploy/stats/tenant`
- **请求方式**: `GET`
- **功能**: 获取租户实例统计数据

## 数据转换

页面包含了一个 `convertAPIInstanceToInstance` 函数，用于将后端 API 返回的数据格式转换为前端组件期望的格式：

- 状态映射：数字状态 → 字符串状态
- 硬件信息格式化
- 应用信息处理
- SSH连接信息设置

## 技术特点

1. **使用 ahooks 的 useRequest**：管理 API 请求状态
2. **响应式设计**：支持不同屏幕尺寸
3. **加载状态**：显示加载动画和骨架屏
4. **错误处理**：集成错误提示
5. **实时刷新**：支持手动刷新数据

## 使用方法

1. 将 `MyInstances.jsx` 文件放在 `src/pages/` 目录下
2. 确保已安装相关依赖：
   - `ahooks`
   - `antd`
   - `lucide-react`
   - `sonner`
3. 在路由中配置页面路径
4. 确保后端接口可用

## 注意事项

1. 需要确保 `src/services/instanceService.ts` 中的接口函数正常工作
2. 某些数据字段（如价格、费用）需要根据实际业务逻辑进行计算
3. SSH连接信息需要从实际接口获取
4. 应用信息的端口和访问URL需要根据实际情况配置

## 后续优化建议

1. 添加实例操作的确认对话框
2. 实现实时监控数据更新
3. 添加批量操作功能
4. 优化移动端显示效果
5. 添加导出功能
