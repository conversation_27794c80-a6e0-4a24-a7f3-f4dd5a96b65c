import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

import { Switch } from '@/components/ui/switch';

import { mockDevices, Device } from '@/data/mockData';

// 扩展设备类型以包含托管相关属性
interface HostedDevice extends Device {
  isListed?: boolean;  // 是否上架
  isRented?: boolean;  // 是否被租用
}

import DeviceConnectionDialog from '@/components/DeviceConnectionDialog';
import {
  Server,
  Cpu,
  HardDrive,
  Zap,
  RefreshCw,
  Plus,
  Eye,
  Power,
  Shield,
  Star,
  TrendingUp,
  Sparkles,
} from 'lucide-react';
import { toast } from 'sonner';

const DeviceHosting: React.FC = () => {
  const [devices, setDevices] = useState<HostedDevice[]>(mockDevices.map(device => ({
    ...device,
    isListed: device.status === 'online', // 添加上架状态
    isRented: false // 添加租用状态
  })));
  const [showConnectionDialog, setShowConnectionDialog] = useState(false);

  // 统计数据
  const stats = [
    {
      title: '已接入设备',
      value: devices.length.toString(),
      icon: Server,
      color: 'text-primary',
      bgColor: 'bg-primary/10',
    },
    {
      title: '总计算核心',
      value: devices.reduce((acc, device) => {
        const cores = parseInt(device.cpu.match(/\d+/)?.[0] || '0');
        return acc + cores;
      }, 0) + ' 核',
      icon: Cpu,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: '本月收益',
      value: '¥1,248',
      icon: Zap,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
    {
      title: '资源利用率',
      value: '86%',
      icon: HardDrive,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
  ];

  // 添加设备
  const handleDeviceAdded = (newDevice: Device) => {
    setDevices(prev => [...prev, { ...newDevice, isListed: false, isRented: false }]);
  };

  // 切换设备上架状态
  const toggleDeviceListing = (deviceId: string) => {
    setDevices(prev => prev.map(device =>
      device.id === deviceId
        ? { ...device, isListed: !device.isListed, status: !device.isListed ? 'online' : 'offline' }
        : device
    ));

    const device = devices.find(d => d.id === deviceId);
    if (device) {
      toast.success(device.isListed ? '设备已下架' : '设备已上架');
    }
  };

  // 删除设备
  const removeDevice = (deviceId: string) => {
    setDevices(prev => prev.filter(device => device.id !== deviceId));
    toast.success('设备已移除');
  };

  const toggleDeviceExclusive = (deviceId: string, exclusive: boolean) => {
    setDevices(prev => prev.map(device =>
      device.id === deviceId ? { ...device, exclusive } : device
    ));

    const device = devices.find(d => d.id === deviceId);
    if (device) {
      if (exclusive) {
        toast.success(`${device.name} 已设为独享使用`);
      } else {
        toast.success(`${device.name} 已取消独享，可上架到市场`);
      }
    }
  };

  const powerOnDevice = (deviceId: string) => {
    setDevices(prev => prev.map(device =>
      device.id === deviceId ? { ...device, status: 'online' as const, exclusive: true } : device
    ));

    const device = devices.find(d => d.id === deviceId);
    if (device) {
      toast.success(`${device.name} 已启动`);
    }
  };



  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50/30 via-purple-50/20 to-indigo-50/30 -m-6 p-6">
      {/* 特殊页面标识横栏 */}
      <div className="mb-6 relative overflow-hidden">
        <div className="bg-gradient-to-r from-primary/10 via-purple-500/10 to-blue-500/10 border border-primary/20 rounded-xl p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-r from-primary to-purple-600 rounded-lg flex items-center justify-center">
              <Shield className="w-5 h-5 text-white" />
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <h3 className="font-semibold text-gray-900">设备托管专区</h3>
                <div className="flex items-center gap-1 px-2 py-1 bg-gradient-to-r from-yellow-400 to-orange-400 text-white text-xs font-medium rounded-full">
                  <Star className="w-3 h-3" />
                  <span>高级功能</span>
                </div>
              </div>
              <p className="text-sm text-gray-600 mt-1">
                专为设备提供商设计的高级管理界面，享受专业级的设备托管服务
              </p>
            </div>
            <div className="hidden md:flex items-center gap-4 text-sm text-gray-500">
              <div className="flex items-center gap-1">
                <TrendingUp className="w-4 h-4 text-green-500" />
                <span>收益优化</span>
              </div>
              <div className="flex items-center gap-1">
                <Sparkles className="w-4 h-4 text-purple-500" />
                <span>智能调度</span>
              </div>
            </div>
          </div>
        </div>
        {/* 装饰性背景元素 */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-primary/5 to-transparent rounded-full -translate-y-16 translate-x-16"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-purple-500/5 to-transparent rounded-full translate-y-12 -translate-x-12"></div>
      </div>

      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">设备托管</h1>
            <p className="text-gray-600 mt-2">接入您的设备到SailFusion Cloud平台，开始赚取收益</p>
          </div>
          <Button
            onClick={() => setShowConnectionDialog(true)}
            className="flex items-center gap-2 bg-gradient-to-r from-primary to-purple-600 hover:from-primary/90 hover:to-purple-600/90 shadow-lg"
          >
            <Plus className="w-4 h-4" />
            添加设备
          </Button>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {stat.title}
                </CardTitle>
                <div className={`w-10 h-10 ${stat.bgColor} rounded-lg flex items-center justify-center`}>
                  <stat.icon className={`w-5 h-5 ${stat.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
              </CardContent>
            </Card>
          ))}
        </div>


        {/* 我的设备 */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>我的设备</CardTitle>
                <CardDescription>管理您已接入的设备</CardDescription>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <RefreshCw className="w-4 h-4 mr-2" />
                  刷新状态
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {/* GPU 服务器 */}
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Cpu className="w-5 h-5 text-primary" />
                GPU 服务器
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {devices.filter(device => device.type === 'GPU').map((device) => (
                  <DeviceCard
                    key={device.id}
                    device={device}
                    onToggleExclusive={toggleDeviceExclusive}
                    onPowerOn={powerOnDevice}
                    onToggleListing={toggleDeviceListing}
                    onRemove={removeDevice}
                  />
                ))}
              </div>
            </div>

            {/* CPU 服务器 */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Server className="w-5 h-5 text-green-600" />
                CPU 服务器
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {devices.filter(device => device.type === 'CPU').map((device) => (
                  <DeviceCard
                    key={device.id}
                    device={device}
                    onToggleExclusive={toggleDeviceExclusive}
                    onPowerOn={powerOnDevice}
                    onToggleListing={toggleDeviceListing}
                    onRemove={removeDevice}
                  />
                ))}
              </div>
            </div>
          </CardContent>
        </Card >

        {/* 设备连接弹框 */}
        <DeviceConnectionDialog
          isOpen={showConnectionDialog}
          onClose={() => setShowConnectionDialog(false)}
          onDeviceAdded={handleDeviceAdded}
        />
      </div>
    </div>
  );
};

// 设备卡片组件
interface DeviceCardProps {
  device: HostedDevice;
  onToggleExclusive: (deviceId: string, exclusive: boolean) => void;
  onPowerOn: (deviceId: string) => void;
  onToggleListing: (deviceId: string) => void;
  onRemove: (deviceId: string) => void;
}

const DeviceCard: React.FC<DeviceCardProps> = ({ device, onToggleExclusive, onPowerOn, onToggleListing, onRemove }) => {
  return (
    <Card className="hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg">{device.name}</CardTitle>
            <div className="flex items-center gap-2 mt-1">
              <div className={`w-2 h-2 rounded-full ${device.status === 'online' ? 'bg-green-500' :
                device.status === 'offline' ? 'bg-red-500' : 'bg-yellow-500'
                }`} />
              <span className="text-sm text-gray-600">
                {device.status === 'online' ? '在线' :
                  device.status === 'offline' ? '离线' : '维护中'}
              </span>
            </div>
          </div>
          <div className={`px-2 py-1 rounded-full text-xs font-medium ${device.type === 'GPU' ? 'bg-primary/10 text-primary' : 'bg-green-100 text-green-700'
            }`}>
            {device.type}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 设备规格 */}
        <div className="grid grid-cols-2 gap-3 text-sm">
          <div>
            <div className="text-gray-500">CPU</div>
            <div className="font-medium">{device.cpu}</div>
          </div>
          <div>
            <div className="text-gray-500">内存</div>
            <div className="font-medium">{device.memory}</div>
          </div>
          <div>
            <div className="text-gray-500">GPU</div>
            <div className="font-medium">{device.gpu}</div>
          </div>
          <div>
            <div className="text-gray-500">存储</div>
            <div className="font-medium">{device.storage}</div>
          </div>
        </div>

        {/* 独享开关 */}
        <div className="flex items-center justify-between py-2 border-t">
          <label className="text-sm font-medium">独享使用</label>
          <Switch
            checked={device.exclusive}
            onCheckedChange={(checked) => onToggleExclusive(device.id, checked)}
            disabled={device.status === 'offline'}
          />
        </div>

        {/* 设备状态 */}
        <div className="flex items-center justify-between py-2 border-t">
          <span className="text-sm font-medium">上架状态</span>
          <div className="flex items-center gap-2">
            <span className="text-xs text-gray-500">
              {device.isListed ? '已上架' : '未上架'}
            </span>
            <Switch
              checked={device.isListed || false}
              onCheckedChange={() => onToggleListing(device.id)}
              disabled={device.status === 'offline' || device.exclusive}
            />
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex gap-2">
          {device.status === 'offline' ? (
            <Button
              size="sm"
              variant="outline"
              className="flex-1"
              onClick={() => onPowerOn(device.id)}
            >
              <Power className="w-4 h-4 mr-1" />
              启动
            </Button>
          ) : (
            <>
              <Button size="sm" variant="outline" className="flex-1">
                <Eye className="w-4 h-4 mr-1" />
                详情
              </Button>
              <Button
                size="sm"
                variant="destructive"
                onClick={() => onRemove(device.id)}
              >
                移除
              </Button>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default DeviceHosting;
