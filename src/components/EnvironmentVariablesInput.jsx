import React from 'react';
import { Form, Input, Button, Row, Col, Card, Typography } from 'antd';
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons';

const { Text } = Typography;

/**
 * 环境变量配置组件
 * 支持动态添加/删除环境变量键值对
 */
const EnvironmentVariablesInput = ({
  value = [],
  onChange,
  disabled = false,
  title = '环境变量配置',
  showTitle = true,
  size = 'default',
}) => {
  // 处理环境变量变化
  const handleChange = newEnvVars => {
    onChange?.(newEnvVars);
  };

  // 添加新的环境变量
  const handleAdd = add => {
    add({ key: '', value: '' });
  };

  // 删除环境变量
  const handleRemove = (remove, name) => {
    remove(name);
  };

  return (
    <Card
      title={showTitle ? title : null}
      size={size}
      className={showTitle ? '' : 'border-0 shadow-none'}
      bodyStyle={showTitle ? {} : { padding: 0 }}
    >
      <Form.List name="environmentVariables" initialValue={value}>
        {(fields, { add, remove }) => (
          <>
            {fields.length === 0 && (
              <div className="text-center py-4 text-gray-500">
                <Text type="secondary">暂无环境变量配置</Text>
              </div>
            )}

            {fields.map(({ key, name, ...restField }, index) => (
              <Row key={key} gutter={16} align="middle" className="mb-3">
                <Col span={10}>
                  <Form.Item
                    {...restField}
                    name={[name, 'key']}
                    label={index === 0 ? '变量名' : ''}
                    rules={[
                      { required: true, message: '请输入变量名' },
                      {
                        pattern: /^[A-Z_][A-Z0-9_]*$/,
                        message:
                          '变量名只能包含大写字母、数字和下划线，且以字母或下划线开头',
                      },
                    ]}
                    className="mb-0"
                  >
                    <Input
                      placeholder="如: DATABASE_URL"
                      disabled={disabled}
                      size={size}
                    />
                  </Form.Item>
                </Col>
                <Col span={10}>
                  <Form.Item
                    {...restField}
                    name={[name, 'value']}
                    label={index === 0 ? '变量值' : ''}
                    rules={[{ required: true, message: '请输入变量值' }]}
                    className="mb-0"
                  >
                    <Input
                      placeholder="如: mysql://localhost:3306/db"
                      disabled={disabled}
                      size={size}
                    />
                  </Form.Item>
                </Col>
                <Col span={4}>
                  {index === 0 && fields.length > 0 && (
                    <div style={{ height: '32px', lineHeight: '32px' }}>
                      操作
                    </div>
                  )}
                  {!disabled && (
                    <Button
                      type="text"
                      danger
                      icon={<MinusCircleOutlined />}
                      onClick={() => handleRemove(remove, name)}
                      size={size}
                      className="flex items-center justify-center"
                    />
                  )}
                </Col>
              </Row>
            ))}

            {!disabled && (
              <Form.Item className="mb-0">
                <Button
                  type="dashed"
                  onClick={() => handleAdd(add)}
                  block
                  icon={<PlusOutlined />}
                  size={size}
                >
                  添加环境变量
                </Button>
              </Form.Item>
            )}

            {/* 提示信息 - 移到 Form.List 内部 */}
            {fields.length > 0 && (
              <div className="mt-3 p-3 bg-blue-50 rounded-lg">
                <Text type="secondary" className="text-xs">
                  💡
                  提示：环境变量将在应用启动时注入到容器中，请确保变量名和值的正确性。
                </Text>
              </div>
            )}
          </>
        )}
      </Form.List>
    </Card>
  );
};

export default EnvironmentVariablesInput;
