import React, { useState } from 'react';
import { Mo<PERSON>, Ta<PERSON>, Card, Button, Input, message } from 'antd';
const { TabPane } = Tabs;
import {
  Server,
  Terminal,
  Copy,
  CheckCircle,
  AlertTriangle
} from 'lucide-react';
import { deviceService } from '@/services/machine';
import { useRequest } from 'ahooks';

interface DeviceConnectionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onDeviceAdded?: () => void;
}

const DeviceConnectionDialog: React.FC<DeviceConnectionDialogProps> = ({
  isOpen,
  onClose,
  onDeviceAdded
}) => {
  const [activeTab, setActiveTab] = useState('ssh');
  const [connectionStatus, setConnectionStatus] = useState<'idle' | 'connecting' | 'success' | 'error'>('idle');

  const [sshForm, setSshForm] = useState({
    machineName: '',
    host: '',
    userName: '',
    passWord: '',
    port: '22'
  });

  const [scriptForm, setScriptForm] = useState({
    machineName: ''
  });

  const [generatedCommand, setGeneratedCommand] = useState('');

  // 使用 useRequest 管理SSH接入
  const { loading: sshLoading, run: runSSHConnect } = useRequest(
    () => deviceService.sshConnect(sshForm),
    {
      manual: true,
      onSuccess: (data: any) => {
        message.success(data || 'SSH 自动接入中');
        setConnectionStatus('success');
        onDeviceAdded?.();
        onClose();
      },
      onError: (error) => {
        message.error('SSH接入失败: ' + (error.message || '未知错误'));
        setConnectionStatus('error');
      },
    }
  );

  const handleSSHConnect = async () => {
    if (!sshForm.machineName || !sshForm.host || !sshForm.userName || !sshForm.passWord) {
      message.error('请填写完整的SSH连接信息');
      return;
    }

    setConnectionStatus('connecting');
    runSSHConnect();
  };

  // 使用 useRequest 管理手动接入命令生成
  const { loading: commandLoading, run: runGenerateCommand } = useRequest(
    () => deviceService.generateCommand(scriptForm),
    {
      manual: true,
      onSuccess: (data: any) => {
        // data 直接是 curl 脚本字符串
        setGeneratedCommand(data || '');
        message.success('接入命令生成成功');
      },
      onError: (error) => {
        message.error('生成命令失败: ' + (error.message || '未知错误'));
      },
    }
  );

  const handleGenerateCommand = () => {
    if (!scriptForm.machineName) {
      message.error('请填写设备名称');
      return;
    }
    runGenerateCommand();
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    message.success('已复制到剪贴板');
  };

  return (
    <Modal
      title={
        <div className="flex items-center gap-2">
          <Server className="w-5 h-5" />
          设备接入
        </div>
      }
      open={isOpen}
      onCancel={onClose}
      footer={null}
      width={1000}
      style={{ top: 20 }}
      styles={{ body: { maxHeight: '90vh', overflow: 'auto' } }}
    >

      <Tabs activeKey={activeTab} onChange={setActiveTab} className="w-full">
        <TabPane tab="SSH 自动接入" key="ssh">

          <div className="space-y-6">
            <Card
              title={
                <div className="flex items-center gap-2">
                  <Terminal className="w-5 h-5" />
                  SSH 连接信息
                </div>
              }
              size="small"
            >
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">设备名称</label>
                    <Input
                      value={sshForm.machineName}
                      onChange={(e) => setSshForm(prev => ({ ...prev, machineName: e.target.value }))}
                      placeholder="例如：我的GPU服务器"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">IP地址</label>
                    <Input
                      value={sshForm.host}
                      onChange={(e) => setSshForm(prev => ({ ...prev, host: e.target.value }))}
                      placeholder="*************"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">用户名</label>
                    <Input
                      value={sshForm.userName}
                      onChange={(e) => setSshForm(prev => ({ ...prev, userName: e.target.value }))}
                      placeholder="root"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">密码</label>
                    <Input
                      type="password"
                      value={sshForm.passWord}
                      onChange={(e) => setSshForm(prev => ({ ...prev, passWord: e.target.value }))}
                      placeholder="SSH密码"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">端口</label>
                    <Input
                      value={sshForm.port}
                      onChange={(e) => setSshForm(prev => ({ ...prev, port: e.target.value }))}
                      placeholder="22"
                    />
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <Button
                    onClick={handleSSHConnect}
                    loading={sshLoading}
                    type="primary"
                    className="flex items-center gap-2"
                  >
                    {connectionStatus === 'connecting' && <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />}
                    {connectionStatus === 'success' && <CheckCircle className="w-4 h-4" />}
                    {connectionStatus === 'error' && <AlertTriangle className="w-4 h-4" />}
                    {connectionStatus === 'idle' ? 'SSH接入' :
                      connectionStatus === 'connecting' ? '接入中...' :
                        connectionStatus === 'success' ? '接入成功' : '接入失败'}
                  </Button>

                  {connectionStatus === 'success' && (
                    <span className="text-sm text-green-600 flex items-center gap-1">
                      <CheckCircle className="w-4 h-4" />
                      设备接入成功，请查看接入记录
                    </span>
                  )}
                </div>
              </div>
            </Card>


          </div>
        </TabPane>

        <TabPane tab="脚本手动接入" key="script">
          <div className="space-y-6">
            <Card
              title="生成接入命令"
              size="small"
            >
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium">设备名称</label>
                  <Input
                    value={scriptForm.machineName}
                    onChange={(e) => setScriptForm(prev => ({ ...prev, machineName: e.target.value }))}
                    placeholder="例如：我的GPU服务器"
                  />
                </div>

                <Button
                  onClick={handleGenerateCommand}
                  loading={commandLoading}
                  type="primary"
                >
                  生成接入命令
                </Button>
              </div>
            </Card>

            {generatedCommand && (
              <Card
                title={
                  <div className="flex items-center gap-2">
                    <Terminal className="w-4 h-4" />
                    设备接入命令
                  </div>
                }
                size="small"
              >
                <div className="space-y-4">
                  {/* 命令显示区域 */}
                  <div className="bg-gray-900 text-green-400 p-4 flex items-center rounded-lg font-mono text-sm relative">
                    <pre className="whitespace-pre-wrap mr-auto flex-1 break-all">{generatedCommand}</pre>
                    <Button
                      type="text"
                      size="small"
                      className="ml-2 text-gray-400 hover:!text-white"
                      onClick={() => copyToClipboard(generatedCommand)}
                    >
                      <Copy className="w-4 h-4" />
                    </Button>
                  </div>

                  {/* 重要提示 */}
                  <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-start gap-2">
                      <AlertTriangle className="w-4 h-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-medium text-yellow-800 mb-1">重要提示</h4>
                        <ul className="text-sm text-yellow-700 space-y-1">
                          <li>• 请确保目标设备已连接到互联网</li>
                          <li>• 命令需要以 <code className="bg-yellow-100 px-1 rounded">root</code> 权限执行</li>
                          <li>• 安装过程可能需要几分钟时间，请耐心等待</li>
                          <li>• 如果安装失败，请检查网络连接和权限设置</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  {/* 操作步骤 */}
                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h4 className="font-medium text-blue-900 mb-3 flex items-center gap-2">
                      <CheckCircle className="w-4 h-4" />
                      详细操作步骤
                    </h4>
                    <ol className="list-decimal list-inside space-y-2 text-sm text-blue-800">
                      <li>
                        <strong>登录目标设备：</strong>
                        <br />
                        <span className="ml-4 text-xs">使用SSH或直接在设备终端登录</span>
                      </li>
                      <li>
                        <strong>切换到root用户：</strong>
                        <br />
                        <code className="ml-4 bg-blue-100 px-2 py-1 rounded text-xs">sudo su -</code>
                        <span className="ml-2 text-xs">或使用 sudo 执行命令</span>
                      </li>
                      <li>
                        <strong>复制并执行命令：</strong>
                        <br />
                        <span className="ml-4 text-xs">将上方命令粘贴到终端并按回车执行</span>
                      </li>
                      <li>
                        <strong>等待安装完成：</strong>
                        <br />
                        <span className="ml-4 text-xs">安装过程中会显示进度信息，请勿中断</span>
                      </li>
                      <li>
                        <strong>验证接入状态：</strong>
                        <br />
                        <span className="ml-4 text-xs">在"接入记录"中查看设备接入状态</span>
                      </li>
                    </ol>
                  </div>

                  {/* 故障排除 */}
                  <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                    <h4 className="font-medium text-gray-900 mb-3">常见问题解决</h4>
                    <div className="space-y-2 text-sm text-gray-700">
                      <div>
                        <strong>权限不足：</strong>
                        <span className="ml-2">请确保使用root权限或在命令前添加sudo</span>
                      </div>
                      <div>
                        <strong>网络连接失败：</strong>
                        <span className="ml-2">检查设备网络连接和防火墙设置</span>
                      </div>
                      <div>
                        <strong>命令执行失败：</strong>
                        <span className="ml-2">可以重新生成命令或联系技术支持</span>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            )}
          </div>
        </TabPane>
      </Tabs>
    </Modal>
  );
};

export default DeviceConnectionDialog;
