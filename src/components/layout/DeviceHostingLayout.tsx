import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Navigate, useLocation, useNavigate } from 'react-router-dom';
import { Menu } from 'antd';
import {
  Server,
  CreditCard,
  BarChart3,
  ArrowLeft,
  HardDrive,
} from 'lucide-react';

interface DeviceHostingLayoutProps {
  children: React.ReactNode;
}

const DeviceHostingLayout: React.FC<DeviceHostingLayoutProps> = ({ children }) => {
  const { user, isLoading } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  const menuItems = [
    {
      key: '/device-hosting',
      icon: <BarChart3 className="w-4 h-4" />,
      label: '概览',
    },
    {
      key: '/device-hosting/my-devices',
      icon: <Server className="w-4 h-4" />,
      label: '我的设备',
    },
    {
      key: '/device-hosting/orders',
      icon: <CreditCard className="w-4 h-4" />,
      label: '订单管理',
    },
  ];

  const handleMenuClick = (e: any) => {
    navigate(e.key);
  };

  const handleBackToMain = () => {
    navigate('/');
  };

  return (
    <div className="h-screen bg-gray-50 flex overflow-hidden">
      {/* 左侧菜单 - 占据全高度 */}
      <div className="w-64 bg-white shadow-sm border-r border-gray-200 flex flex-col">
        {/* 头部 */}
        <div className="p-4 border-b border-gray-200 flex-shrink-0">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-blue-600 rounded-xl flex items-center justify-center">
              <HardDrive className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-lg font-bold text-gray-900">托管设备</h1>
              <p className="text-sm text-gray-500">设备管理中心</p>
            </div>
          </div>

          <button
            onClick={handleBackToMain}
            className="flex items-center space-x-2 text-sm text-gray-600 hover:text-gray-900 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>返回主界面</span>
          </button>
        </div>

        {/* 菜单 - 可滚动 */}
        <div className="flex-1  p-2">
          <Menu
            mode="inline"
            selectedKeys={[location.pathname]}
            items={menuItems}
            onClick={handleMenuClick}
            className="!border-none border-transparent h-full overflow-y-auto"
          />
        </div>
      </div>

      {/* 主内容区域 - 内部滚动 */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <main className="flex-1 overflow-y-auto p-4">
          <div className="max-w-full mx-auto px-2">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default DeviceHostingLayout;
