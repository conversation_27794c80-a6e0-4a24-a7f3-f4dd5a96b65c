@layer tailwind-base, antd;

@layer tailwind-base {
  @tailwind base;
}
@tailwind components;
@tailwind utilities;

html body[data-scroll-locked] {
  padding-right: 0 !important;
  --removed-body-scroll-bar-size: 0px !important;
  margin-right: 0 !important;
}
@layer base {
  :root {
    /* 基础颜色 - 以主题色 #0d22ef 为基础 */
    --background: #ffffff;
    --foreground: #1a1a1a;
    --card: #ffffff;
    --card-foreground: #1a1a1a;
    --popover: #ffffff;
    --popover-foreground: #1a1a1a;

    /* 主题色系 - 蓝紫色 #0d22ef */
    --primary: #0d22ef;
    --primary-foreground: #ffffff;
    --primary-50: #f0f4ff;
    --primary-100: #e0e9ff;
    --primary-200: #c7d7fe;
    --primary-300: #a5bbfc;
    --primary-400: #8195f8;
    --primary-500: #0d22ef;
    --primary-600: #0b1ed6;
    --primary-700: #0918b3;
    --primary-800: #071490;
    --primary-900: #051076;

    /* 次要颜色 */
    --secondary: #f8fafc;
    --secondary-foreground: #1a1a1a;
    --muted: #f1f5f9;
    --muted-foreground: #64748b;
    --accent: #f1f5f9;
    --accent-foreground: #1a1a1a;

    /* 状态颜色 */
    --destructive: #ef4444;
    --destructive-foreground: #ffffff;
    --success: #10b981;
    --success-foreground: #ffffff;
    --warning: #f59e0b;
    --warning-foreground: #ffffff;

    /* 边框和输入 */
    --border: #e2e8f0;
    --input: #e2e8f0;
    --ring: #0d22ef;
    --radius: 0.5rem;
  }

  .dark {
    --background: #0f172a;
    --foreground: #f8fafc;
    --card: #1e293b;
    --card-foreground: #f8fafc;
    --popover: #1e293b;
    --popover-foreground: #f8fafc;
    --primary: #3b82f6;
    --primary-foreground: #ffffff;
    --secondary: #334155;
    --secondary-foreground: #f8fafc;
    --muted: #334155;
    --muted-foreground: #94a3b8;
    --accent: #334155;
    --accent-foreground: #f8fafc;
    --destructive: #dc2626;
    --destructive-foreground: #f8fafc;
    --border: #334155;
    --input: #334155;
    --ring: #3b82f6;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* SailFusion Cloud Design System - 基于主题色 #0d22ef 的现代计算平台 */

@layer utilities {
  /* 自定义渐变变量 */
  :root {
    --gradient-primary: linear-gradient(135deg, #0d22ef, #3b82f6);
    --gradient-accent: linear-gradient(135deg, #10b981, #059669);
    --gradient-hero: linear-gradient(135deg, #ffffff 0%, #f0f4ff 100%);
    --gradient-card: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);

    /* 阴影效果 */
    --shadow-primary: 0 0 30px rgba(13, 34, 239, 0.1);
    --shadow-accent: 0 0 30px rgba(16, 185, 129, 0.1);
    --shadow-card: 0 4px 24px rgba(26, 26, 26, 0.05);

    /* 动画变量 */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter antialiased;
  }
}

@layer components {
  /* Hero button variants */
  .btn-hero {
    @apply bg-gradient-to-r from-primary to-primary/80 text-primary-foreground;
    @apply border border-primary/20 rounded-lg px-6 py-3 font-medium;
    @apply transition-all duration-300 hover:shadow-[var(--shadow-primary)];
    @apply hover:scale-105 active:scale-95;
  }

  .btn-accent {
    @apply bg-gradient-to-r from-accent to-accent/80 text-accent-foreground;
    @apply border border-accent/20 rounded-lg px-6 py-3 font-medium;
    @apply transition-all duration-300 hover:shadow-[var(--shadow-accent)];
    @apply hover:scale-105 active:scale-95;
  }

  /* Card variants */
  .card-compute {
    @apply bg-gradient-to-br from-card to-card/50;
    @apply border border-border/50 rounded-xl p-6;
    @apply shadow-[var(--shadow-card)] backdrop-blur-sm;
    @apply transition-all duration-300 hover:shadow-[var(--shadow-primary)];
    @apply hover:border-primary/30;
  }

  .card-glow {
    @apply relative overflow-hidden;
  }

  .card-glow::before {
    @apply content-[''] absolute inset-0 rounded-xl;
    @apply bg-gradient-to-r from-primary/10 via-transparent to-accent/10;
    @apply opacity-0 transition-opacity duration-300;
  }

  .card-glow:hover::before {
    @apply opacity-100;
  }

  /* Text gradients */
  .text-gradient-primary {
    @apply bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent;
  }

  .text-gradient-accent {
    @apply bg-gradient-to-r from-accent to-accent/70 bg-clip-text text-transparent;
  }

  /* Animations */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite alternate;
  }

  /* Status indicators */
  .status-online {
    @apply bg-accent text-accent-foreground;
  }

  .status-offline {
    @apply bg-muted text-muted-foreground;
  }

  .status-busy {
    @apply bg-destructive text-destructive-foreground;
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse-glow {
  0% {
    box-shadow: 0 0 20px rgba(13, 34, 239, 0.3);
  }
  100% {
    box-shadow: 0 0 30px rgba(13, 34, 239, 0.6);
  }
}

/* AI推荐按钮扫光动画 */
@keyframes sweep {
  0% {
    transform: translateX(-100%) skewX(-12deg);
  }
  100% {
    transform: translateX(200%) skewX(-12deg);
  }
}

/* 添加扫光动画到Tailwind */
.animate-sweep {
  animation: sweep 1.5s ease-in-out;
}

.ant-segmented-item-label {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}
