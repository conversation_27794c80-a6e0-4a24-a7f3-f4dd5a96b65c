import { message } from 'antd'
import axios from 'axios'
// import { getNavigate } from './utils'

// 创建 axios 实例
const http = axios.create({
  apiUrl: '/basic-api',
  timeout: 60 * 1000,
})


function fakeAuthHeaders (headers) {
  const auth = {
    "User-Id": 1,
    "User-Name": "小明",
    "Tenant-Id": 1,
    "Tenant-Name": "超管团队",
    "Role-Id": 1,
    "Role-Name": "admin",
    "Role-Code": "CPM_ADMIN"
  }

  Object.keys(auth).forEach(key => {
    headers[key] = encodeURI(auth[key])
  })
}

// 请求拦截器
http.interceptors.request.use(config => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `${token}`;

    if (config.requestOptions?.withToken !== false) {
      const authScheme = config.customAuthScheme;
      // jwt token
      config.headers.Authorization = authScheme ? `${authScheme} ${token}` : token;
    }
  }
  fakeAuthHeaders(config.headers)
  if (config.apiUrl) {
    config.url = `${config.apiUrl}${config.url}`;
  }
  return config
})

// 错误处理函数
export const onHandleError = (code, msg, data) => {
  if (code === 401 || code === 403) {
    message.warning('登录已过期，请重新登录')
    // clearUserInfo();
    // 跳转到登录页
    // getNavigate()('/login')
  } else {
    message.warning(msg)
  }
}

// 响应拦截器
http.interceptors.response.use(
  (response) => {
    const { silent, full, blob } = response.config;
    const { code, msg, data } = response.data

    if (blob) {
      return response;
    }

    if (code == 0) {
      // 如果需要完整响应，直接返回
      if (full) {
        return response.data;
      }
      return data
    }

    if (!silent) {
      onHandleError(code, msg, data);
    }
    throw response;
  },
  (error) => {
    if (!error.config || !error.config.silent) {
      onHandleError(error.response?.status, error.message, error.response?.data);
    }
    return Promise.reject(error);
  }
)

export default http
